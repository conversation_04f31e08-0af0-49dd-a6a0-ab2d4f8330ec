import datetime
import sys
import time
import traceback
import logging
from typing import Optional, Dict

from selenium.webdriver import Firefox
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.wait import WebDriverWait
from prometheus_client import Gauge, Counter

from common import (
    TransactionInfoDao,
    Transaction,
    main_app,
    COMPANY_NAME,
    COMPANY_NUMBER,
    TAX_NUMBER,
    EXCHANGE_MAPPING,
)

# Constants
OKX_SOURCE_ID = 505
DEPOSIT_REASON = "Internal transfer"
DEFAULT_WAIT_TIMEOUT = 10
PAGE_HISTORY_LIMIT = 2

# URLs
HOST = "https://tr.okx.com"
DEPOSIT_HISTORY = f"{HOST}/en/balance/recharge"
LOGIN_URL = f"{HOST}/account/login"

# Source exchange names mapping
SOURCE_EXCHANGE_NAME = {
    "btcturk_turkey": "BTCTurk Kripto",
    "bybit_turkey": "Bybit Türkiye",
    "binance_turkey": "Binance TR",
}

# Setup logging
log = logging.getLogger("okx_tr_clicker")
log.addHandler(logging.StreamHandler(sys.stdout))


class OkxTrClicker:
    """OKX Turkey automated clicker for travel rule compliance"""

    def __init__(self, driver: Firefox, trx_dao: TransactionInfoDao, config: Dict):
        self.driver: Firefox = driver
        self.trx_dao: TransactionInfoDao = trx_dao
        self.okx_tab: Optional[str] = None

        namespace = "gt_clicker"
        self._mon_loop_end = Gauge("loop_end", documentation="", namespace=namespace)
        self._mon_loop_end.set_to_current_time()
        self._mon_deposit_details_req = Gauge("deposit_details_req", documentation="", namespace=namespace)
        self._mon_deposit_details_provided = Counter("deposit_details_provided", documentation="", namespace=namespace)
        self._mon_is_logged_in = Gauge("logged_in", documentation="", namespace=namespace)
        self._mon_errors = Counter("errors", documentation="", namespace=namespace)

    def sleep(self, sleep_for, comment: Optional[str] = None):
        log.debug(f"sleeping for {sleep_for} ({comment})")
        time.sleep(sleep_for)

    def switch_to_okx_tab(self):
        for wh in self.driver.window_handles:
            self.driver.switch_to.window(wh)
            url = self.driver.current_url
            log.info(f"Tab {wh} current url = {url}")
            if "tr.okx.com" in url:
                self.okx_tab = wh
                return
        raise Exception("Failed to find OKX Turkey tab")

    def check_language(self):
        """Ensure we're using English language on OKX Turkey"""
        try:
            # Check if language selector exists and set to English
            lang_selector = self.driver.find_elements(By.XPATH, "/html/body/header/div/div/nav/ul/li[5]/ul/div[3]/button")
            if lang_selector:
                lang_selector[0].click()
                self.sleep(1, "language selector open")
                english_option = self.driver.find_elements(By.XPATH, '//*[@id="language_en_US"]')
                if english_option[0].get_attribute("aria-current") == "true":
                    log.debug("English option is selected")
                    close_btn = self.driver.find_elements(By.XPATH, '//*[@id="okdDialogCloseBtn"]')
                    if close_btn:
                        close_btn[0].click()
                    else:
                        log.debug("Close button not found")
                    self.sleep(2, "language selector closed")
            else:
                english_option[0].click()
                self.sleep(3, "language selected")

        except Exception as e:
            log.debug(f"Language check failed: {e}")

    def check_login_status(self):
        if self.driver.current_url != DEPOSIT_HISTORY:
            self.driver.get(DEPOSIT_HISTORY)
        
        self.driver.refresh()
        self.sleep(5, "waiting for login refresh to finish")

        self.check_language()
        log.info(f"Current title: {self.driver.title}")
        
        # Check for login indicators
        login_indicators = [
            "//a[contains(text(), 'Log in')]",
            "//button[contains(text(), 'Login')]",
            "//div[contains(@class, 'login-btn')]"
        ]
        
        for indicator in login_indicators:
            if self.driver.find_elements(By.XPATH, indicator):
                self._mon_is_logged_in.set(0)
                raise Exception("OKX Turkey not logged in")

        self._mon_is_logged_in.set(1)
        log.info("OKX Turkey login status: OK")

    def get_transaction_details(self, row_elem) -> Transaction:
        """Extract transaction details from a table row"""

        try:
            asset = row_elem.find_element(By.CSS_SELECTOR, "td:nth-child(3)").text.strip()
            #get the txid from href in view button
            view_btn = row_elem.find_element(By.XPATH, ".//a[contains(., 'View')]")
            txid = view_btn.get_attribute("href").split("/")[-1]
            ts = row_elem.find_element(By.CSS_SELECTOR, "td:nth-child(1)").text
            ts = datetime.datetime.strptime(ts, "%m/%d/%Y, %H:%M:%S")
                
        except Exception as e:
            log.warning(f"Failed to extract transaction details: {e}")

        return Transaction(
            asset=asset,
            amount=None,
            txid=txid,
            address=None,
            fee=None,
            ts=ts,
            source=OKX_SOURCE_ID,
            trx_id=None,
        )

    def process_deposits(self):
        self.driver.get(DEPOSIT_HISTORY)
        self.sleep(10, "deposit load")

        PAGE_HISTORY = 2
        total_details = 0
        try:
            for _ in range(PAGE_HISTORY):
                details_required = []

                for elem in self.driver.find_elements(
                    By.XPATH, "//tbody/tr[@data-row-key]"
                ):
                    status = elem.find_element(By.CSS_SELECTOR, "td:nth-child(6)").text.strip()
                    log.debug(f"Status: {status}")
                    if status == "On hold":
                        details_required.append(elem.get_attribute("data-row-key"))
                        log.debug(f"Details required: {details_required[-1]}")

                total_details += len(details_required)

                log.info(f"Details required for = {len(details_required)}")

                for row_id in details_required:
                    log.debug(f"Looking for {row_id}")
                    data_row = self.driver.find_element(By.XPATH, f"//tbody/tr[@data-row-key = '{row_id}']")
                    transaction = self.get_transaction_details(data_row)
                    log.debug(f"Transaction: {transaction}")
                    source_exchange = self.trx_dao.get_deposit_source_exchange(transaction)
                    if source_exchange is None:
                        log.info(f"Unable to find transfer for deposit {transaction} (row_id={row_id})")
                        continue
                    log.debug(f"Source exchange: {source_exchange}")

                    try:
                        #open the information form
                        expanded_row_xpath = (
                            f"//tr[@data-row-key='{row_id}']"
                            "/following-sibling::tr[contains(@class,'balance_okui-table-expanded-row')][1]"
                        )
                        link_xpath = (
                            expanded_row_xpath +
                            "//div[@role='button' and contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',"
                            "                                   'abcdefghijklmnopqrstuvwxyz'),"
                            "                      'submit sender information')]"
                        )
                        link = self.driver.find_element(By.XPATH, link_xpath)
                        link.click()

                        wait = WebDriverWait(self.driver, 10)

                        dialog = wait.until(
                            EC.visibility_of_element_located(
                                (By.CSS_SELECTOR,
                                "div[role='dialog'][aria-modal='true'].compliance-dialog-window")
                            )
                        )

                        self.sleep(2, "wait for dialog fully load")

                        choice = dialog.find_element(
                            By.XPATH,
                            ".//button[contains(@class,'InputChoice_item')"
                            "and contains(translate(.,'ABCDEFGHIJKLMNOPQRSTUVWXYZ','abcdefghijklmnopqrstuvwxyz'),"
                            "               'exchange or platform')]"
                        )
                        self.sleep(2, "wait for choice to be visible")
                        choice.click()
                        self.sleep(2, "wait for choice to be clicked")

                        next_btn = dialog.find_element(By.XPATH, ".//button[normalize-space(.)='Next']")
                        self.sleep(2, "wait for next button to be visible")
                        next_btn.click()
                        self.sleep(4, "wait for next button to be clicked")

                        ui_name = SOURCE_EXCHANGE_NAME[source_exchange]

                        dropdown = dialog.find_element(
                            By.XPATH,
                            ".//div[@class='compliance compliance-input compliance-input-xl compliance-select-value kyc-select-value-cont']"
                        )
                        dropdown.click()

                        self.sleep(2, "wait for dropdown to be clicked")

                        search_input = self.driver.switch_to.active_element

                        search_input.clear()
                        search_input.send_keys(ui_name)
                        self.sleep(2, "wait for search to be entered")
                        search_input.send_keys(Keys.ENTER)
                        self.sleep(2, "wait for search to be entered")

                        # Click next again
                        next_btn = dialog.find_element(By.XPATH, ".//button[normalize-space(.)='Next']")
                        self.sleep(2, "wait for next button to be visible")
                        next_btn.click()
                        self.sleep(4, "wait for next button to be clicked")

                        button = dialog.find_element(By.XPATH, ".//button[contains(@class, 'InputChoice_item__aa-H1') and contains(., 'Yes, I am sending it from my account')]")
                        button.click()
                        self.sleep(2, "wait for button to be clicked")

                        # Click next again
                        next_btn = dialog.find_element(By.XPATH, ".//button[normalize-space(.)='Next']")
                        self.sleep(2, "wait for next button to be visible")
                        next_btn.click()
                        self.sleep(4, "wait for next button to be clicked")

                        self._mon_deposit_details_provided.inc()

                    except Exception as e:
                        self._mon_errors.inc()
                        log.error(f"Failed to populate deposit details: {e}")
                        log.debug(traceback.format_exc())

                self.driver.find_element(By.XPATH, "/html/body/div[1]/div/div/div/div[2]/div[2]/div/div[2]/div/div/nav/button[2]").click()
                self.sleep(10, "Load next deposit page")
        finally:
            self._mon_deposit_details_req.set(total_details)

    def run(self):
        """Main execution loop"""
        log.info("Starting OKX Turkey clicker")

        while True:
            try:
                self.switch_to_okx_tab()
                self.check_login_status()
                self.process_deposits()

                self._mon_loop_end.set_to_current_time()
                log.info("Cycle completed successfully")

            except Exception as e:
                self._mon_errors.inc()
                log.error(f"Error in main loop: {e}")
                log.debug(traceback.format_exc())

            self.sleep(60, "end of loop")


def main():
    main_app(OkxTrClicker)


if __name__ == "__main__":
    main()
