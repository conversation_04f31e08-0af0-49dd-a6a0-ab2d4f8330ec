import sys, logging, time
import datetime
from yaml import safe_load
import traceback

from typing import Optional
from dataclasses import dataclass
from psycopg_pool import ConnectionPool

from selenium.webdriver import Firefox
from selenium.webdriver.firefox.service import Service as FirefoxService

from prometheus_client import start_http_server, Gauge, Counter


COMPANY_NAME = "Gravity Team Elektronik Hizmetler Anonim Şirketi"
COMPANY_NUMBER = "0411093512600001"
TAX_NUMBER = "4110935126"


EXCHANGE_MAPPING = {
    "btcturk_turkey": "BtcTurk | Kripto",
    "bybit_turkey": "Bybit",
    "okx_turkey": "OKX",
    "binance_turkey": "Binance TR",
}


@dataclass
class Transaction:
    source: int
    asset: str
    ts: datetime.datetime
    amount: Optional[float]
    txid: Optional[str]
    address: Optional[str]
    trx_id: Optional[str]
    fee: Optional[float]


class TransactionInfoDao:

    def __init__(self, pool: ConnectionPool):
        self._pool = pool

    def get_deposit_source_exchange(self, transaction: Transaction) -> Optional[str]:
        with self._pool.connection() as conn:
            with conn.cursor() as cur:
                params = {
                    "ts": transaction.ts,
                    "asset": transaction.asset,
                    "source": transaction.source,
                }
                if transaction.amount is not None:
                    params["amount"] = transaction.amount
                if transaction.txid:
                    params["txid"] = transaction.txid
                if transaction.trx_id:
                    params["trx_id"] = transaction.trx_id
                if transaction.address:
                    params["address"] = transaction.address

                cur.execute(
                    f"""
                    SELECT sm.exchange
                      FROM capman.exchange_deposits ed
                     INNER JOIN capman.exchange_Transfer_matches etm ON ed.gt_id = etm.deposit_id AND etm.gt_timestamp > ed.gt_timestamp
                     INNER JOIN capman.transfers t ON t.gt_id = etm.transfer_id AND t.gt_timestamp < ed.gt_timestamp AND t.gt_timestamp  > NOW() - '30 days'::INTERVAL
                     INNER JOIN capman.sources_map sm ON sm.gt_source = t.gt_source_from
                     WHERE ed.gt_timestamp > %(ts)s - '12 hour'::INTERVAL
                       {'AND ed.txid = %(txid)s' if transaction.txid else ''}
                       {'AND ed.external_id = %(trx_id)s' if transaction.trx_id else ''}
                       {'AND ed.source_address = %(address)s' if transaction.address else ''}
                       AND ed.gt_asset=%(asset)s
                       {'AND abs(ed.gt_amount - %(amount)s)/%(amount)s < 0.00001' if transaction.amount is not None else ''}
                       AND ed.gt_source = %(source)s
                    """,
                    params,
                )
                row = cur.fetchone()
                if row:
                    return row[0]

        return None

    def get_withdrawal_target_exchange(self, transaction: Transaction) -> Optional[str]:
        with self._pool.connection() as conn:
            with conn.cursor() as cur:
                params = {
                    "ts": transaction.ts,
                    "asset": transaction.asset,
                    "amount": transaction.amount,
                    "source": transaction.source,
                }

                if transaction.txid:
                    params["txid"] = transaction.txid
                if transaction.trx_id:
                    params["trx_id"] = transaction.trx_id
                if transaction.address:
                    params["address"] = transaction.address

                cur.execute(
                    f"""
                    SELECT sm.exchange
                      FROM capman.exchange_withdrawals ew
                     INNER JOIN capman.exchange_transfer_matches etm ON ew.gt_id = etm.withdrawal_id AND etm.gt_timestamp > ew.gt_timestamp
                     INNER JOIN capman.transfers t ON t.gt_id = etm.transfer_id AND t.gt_timestamp < ew.gt_timestamp AND t.gt_timestamp  > NOW() - '30 days'::INTERVAL
                     INNER JOIN capman.sources_map sm ON sm.gt_source = t.gt_source_to
                     WHERE ew.gt_timestamp > %(ts)s - '1 hour'::INTERVAL
                       {'AND ew.txid = %(txid)s' if transaction.txid else ''}
                       {'AND ew.external_id = %(trx_id)s' if transaction.trx_id else ''}
                       {'AND ew.gt_address = %(address)s' if transaction.address else ''}
                       AND ew.gt_address = %(address)s
                       AND ew.gt_asset=%(asset)s
                       AND abs(ew.gt_amount - %(amount)s)/%(amount)s < 0.00001
                       AND ew.gt_source = %(source)s
                    """,
                    params,
                )
                row = cur.fetchone()
                if row:
                    return row[0]
        return None


def connect_to_browser(port: str):
    firefox_service = FirefoxService(
        service_args=[
            "--connect-existing",
            "--log",
            "debug",
            "--marionette-host",
            "localhost",
            "--marionette-port",
            port,
        ],
    )

    return Firefox(service=firefox_service)


def main_app(clicker_class):
    with open(sys.argv[1], "r") as config_fh:
        config = safe_load(config_fh)

    if "log" in config:
        level = {"debug": logging.DEBUG, "info": logging.INFO, "error": logging.ERROR}[
            config["log"].get("level", "info")
        ]
        logging.basicConfig(level=level, filename=config["log"]["path"])
    else:
        logging.basicConfig(level=logging.DEBUG)

    start_http_server(config["prometheus"]["port"], config["prometheus"].get("addr", "0.0.0.0"))

    # Convert individual db parameters to connection string
    db_config = config["db"]
    conninfo = f"host={db_config['host']} port={db_config['port']} user={db_config['user']} password={db_config['password']} dbname={db_config['dbname']}"
    
    # Extract pool-specific parameters
    pool_params = {}
    if "min_size" in db_config:
        pool_params["min_size"] = db_config["min_size"]
    if "max_size" in db_config:
        pool_params["max_size"] = db_config["max_size"]
    
    with ConnectionPool(conninfo, **pool_params) as pool:
        driver = connect_to_browser(str(config["marionette_port"]))
        clicker = clicker_class(driver, TransactionInfoDao(pool), config)
        clicker.run()


class ClickerBase:

    def __init__(self, driver: Firefox, base_url: str, refresh_url: str, namespace: str, log: logging.Logger):
        self.base_url = base_url
        self.refresh_url = refresh_url
        self.driver = driver
        self.log = log

        self.driver: Firefox = driver
        self.exchange_tab: Optional[str] = None

        self._mon_loop_end = Gauge("loop_end", documentation="", namespace=namespace)
        self._mon_loop_end.set_to_current_time()

        self._mon_is_logged_in = Gauge("logged_in", documentation="", namespace=namespace)
        self._mon_errors = Counter("errors", documentation="", namespace=namespace)

    def sleep(self, sleep_for, comment: Optional[str] = None):
        self.log.debug(f"sleeping for {sleep_for} ({comment})")
        time.sleep(sleep_for)

    def switch_to_exchange_tab(self):
        for wh in self.driver.window_handles:
            self.driver.switch_to.window(wh)
            url = self.driver.current_url
            self.log.info(f"Tab {wh} current url = {url}")
            if self.base_url in url:
                self.bybit_tab = wh
                return
        raise Exception("Failed to find bybit tab")

    def check_login_status(self):
        if self.driver.current_url != self.refresh_url:
            self.driver.get(self.refresh_url)
        self.driver.refresh()
        self.sleep(2, "waiting for login refresh to finish")
        self.log.info(f"Current title {self.driver.title}")
        if not self.is_logged_in():
            self._mon_is_logged_in.set(0)
            raise Exception("Exchange not logged in")
        else:
            self._mon_is_logged_in.set(1)

    def is_logged_in(self) -> bool:
        return False

    def run(self):

        while True:
            try:
                self.switch_to_exchange_tab()
                self.check_login_status()
                self.process_deposits()
                self.driver.refresh()
                self._mon_loop_end.set_to_current_time()
            except Exception:
                self._mon_errors.inc()
                self.log.error(f"Failed to click: {traceback.format_exc()}")

            self.sleep(60, "end of loop")

    def process_deposits(self):
        pass

    def process_withdrawals(self):
        pass
