#!/usr/bin/env python3
"""
Helper script to start Firefox with marionette enabled for OKX Turkey clicker testing
"""
import subprocess
import sys
import time
import os
import logging

logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)

def start_firefox_with_marionette():
    """Start Firefox with marionette enabled"""
    
    # Profile path
    profile_path = r"C:\Users\<USER>\AppData\Roaming\Mozilla\Firefox\Profiles\miivh04v.Selenium-okx-tr-clicker"
    
    # Firefox executable paths (common locations)
    firefox_paths = [
        r"C:\Program Files\Mozilla Firefox\firefox.exe",
        r"C:\Program Files (x86)\Mozilla Firefox\firefox.exe",
        r"C:\Users\<USER>\AppData\Local\Mozilla Firefox\firefox.exe",
    ]
    
    # Find Firefox executable
    firefox_exe = None
    for path in firefox_paths:
        expanded_path = os.path.expandvars(path)
        if os.path.exists(expanded_path):
            firefox_exe = expanded_path
            break
    
    if not firefox_exe:
        log.error("❌ Firefox executable not found. Please install Firefox or update the path.")
        return False
    
    log.info(f"🔍 Found Firefox at: {firefox_exe}")
    
    # Check if profile exists
    if not os.path.exists(profile_path):
        log.warning(f"⚠️ Profile path not found: {profile_path}")
        log.info("Firefox will create a new profile automatically")
    
    # Firefox command with marionette
    firefox_command = [
        firefox_exe,
        "--marionette",
        "--marionette-port", "2828",
        "--new-instance",
        "--no-remote",
        "--profile", profile_path,
        "https://tr.okx.com/en"
    ]
    
    try:
        log.info("🚀 Starting Firefox with marionette...")
        log.info(f"Command: {' '.join(firefox_command)}")
        
        # Start Firefox
        process = subprocess.Popen(
            firefox_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP
        )
        
        log.info("✅ Firefox started successfully!")
        log.info(f"Process ID: {process.pid}")
        log.info("🌐 Firefox should open with OKX Turkey page")
        log.info("🔌 Marionette enabled on port 2828")
        
        # Wait a bit for Firefox to start
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is None:
            log.info("✅ Firefox is running")
            return True
        else:
            log.error("❌ Firefox process exited unexpectedly")
            return False
            
    except Exception as e:
        log.error(f"❌ Failed to start Firefox: {e}")
        return False

def main():
    """Main function"""
    print("🔥 Firefox Marionette Starter for OKX Turkey Clicker")
    print("=" * 60)
    print("This script starts Firefox with marionette enabled on port 2828")
    print("Required for OKX Turkey clicker automation testing")
    print()
    
    # Start Firefox
    success = start_firefox_with_marionette()
    
    if success:
        print("\n✅ Firefox started successfully!")
        print("📋 Next steps:")
        print("   1. Firefox should be open with OKX Turkey page")
        print("   2. You can now run test scripts:")
        print("      python test_okx_basic.py")
        print("      python test_okx_interactive.py")
        print("   3. Or run the main clicker:")
        print("      python okx_turkey_clicker_basic.py config.yml")
        print()
        print("ℹ️ Keep this terminal open if you want to see Firefox logs")
        print("🔴 Press Ctrl+C to stop Firefox")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Stopping Firefox...")
            
    else:
        print("\n❌ Failed to start Firefox")
        print("💡 Troubleshooting:")
        print("   1. Make sure Firefox is installed")
        print("   2. Update Firefox paths in the script if needed")
        print("   3. Try running Firefox manually with marionette")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 